'use client';

import Image from "next/image";
import { useState, useRef } from "react";
import { Button } from "../ui/button";
import {Upload, Camera, Share2 } from "lucide-react";
import MultiAnalysisResults from "../ui/multi-analysis-results";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { compressImageToBase64, isValidImageFile, isValidImageSize } from "../../lib/image-utils";

interface AttractivenessResult {
  overall_score: number;
  factors: any[];
  analysis_text: string;
  overall_recommendations: string;
}

interface MultiAnalysisResult {
  analyses: {
    type: string;
    overall_score?: number;
    factors?: any[];
    analysis_text?: string;
    overall_recommendations?: string;
    shape?: string;
    confidence_score?: number;
    measurements?: any;
    styling_tips?: any;
    celebrity_examples?: string[];
    description?: string;
  }[];
}

type AnalysisResult = AttractivenessResult | MultiAnalysisResult;

export default function Hero() {
  const router = useRouter();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [result, setResult] = useState<AnalysisResult | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const [captureMode, setCaptureMode] = useState<'upload' | 'camera'>('upload');
  const [showCamera, setShowCamera] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type.startsWith('image/')) {
        setSelectedFile(file);
      } else {
        toast.error("Please select an image file");
      }
    }
  };

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file type
      if (!isValidImageFile(file)) {
        toast.error("Please select a valid image file (JPEG, PNG, WebP)");
        return;
      }

      // Validate file size (10MB max before compression)
      if (!isValidImageSize(file, 10)) {
        toast.error("Image size must be less than 10MB");
        return;
      }

      try {
        // Show compression progress
        toast.info("Compressing image...");

        // Compress the image
        const compressed = await compressImageToBase64(file, 1024, 1024, 0.8);

        // Create a new file from the compressed base64
        const compressedFile = new File(
          [new Uint8Array(atob(compressed.base64.split(',')[1]).split('').map(c => c.charCodeAt(0)))],
          file.name,
          { type: 'image/jpeg' }
        );

        setSelectedFile(compressedFile);

        // Show compression stats
        toast.success(
          `Image compressed: ${(compressed.originalSize / 1024 / 1024).toFixed(1)}MB → ${(compressed.size / 1024 / 1024).toFixed(1)}MB (${compressed.compressionRatio.toFixed(1)}% reduction)`
        );
      } catch (error) {
        console.error('Image compression failed:', error);
        toast.error("Failed to compress image. Please try a different image.");
      }
    }
  };

  const handleChooseFileClick = () => {
    fileInputRef.current?.click();
  };

  const handleAnalysis = async () => {
    if (!selectedFile) {
      toast.error("Please select an image first");
      return;
    }

    setIsAnalyzing(true);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('image', selectedFile);
      formData.append('analysisType', 'attractive-score');

      const response = await fetch('/api/analyze-image', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        // Save result to database and redirect to results page
        try {
          // Convert image to base64 for storage
          const compressed = await compressImageToBase64(selectedFile, 800, 800, 0.7);

          const saveResponse = await fetch('/api/results', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              analysis_data: data,
              image_base64: compressed.base64,
            }),
          });

          if (saveResponse.ok) {
            const { id } = await saveResponse.json();
            toast.success("Analysis complete!");
            router.push(`/results/${id}`);
          } else {
            // Fallback to showing results inline if save fails
            setResult(data);
            toast.success("Analysis complete!");
          }
        } catch (saveError) {
          console.error('Error saving result:', saveError);
          // Fallback to showing results inline
          setResult(data);
          toast.success("Analysis complete!");
        }
      } else {
        toast.error(data.error || "Analysis failed");
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error("Failed to analyze image. Please try again.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const startCamera = async () => {
    setShowCamera(true);
    setCaptureMode('camera');
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user' } // Use front camera on mobile
      });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      toast.error('Unable to access camera. Please make sure you have granted camera permissions.');
      setShowCamera(false);
    }
  };

  const capturePhoto = async () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (context) {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);

        canvas.toBlob(async (blob) => {
          if (blob) {
            try {
              const originalFile = new File([blob], 'captured-photo.jpg', { type: 'image/jpeg' });

              // Show compression progress
              toast.info("Compressing captured image...");

              // Compress the captured image
              const compressed = await compressImageToBase64(originalFile, 1024, 1024, 0.8);

              // Create compressed file
              const compressedFile = new File(
                [new Uint8Array(atob(compressed.base64.split(',')[1]).split('').map(c => c.charCodeAt(0)))],
                'captured-photo.jpg',
                { type: 'image/jpeg' }
              );

              setSelectedFile(compressedFile);

              // Show compression stats
              toast.success(
                `Image compressed: ${(compressed.originalSize / 1024 / 1024).toFixed(1)}MB → ${(compressed.size / 1024 / 1024).toFixed(1)}MB`
              );
            } catch (error) {
              console.error('Image compression failed:', error);
              // Fallback to original file if compression fails
              const file = new File([blob], 'captured-photo.jpg', { type: 'image/jpeg' });
              setSelectedFile(file);
              toast.warning("Image compression failed, using original image");
            }

            // Stop camera stream
            if (video.srcObject) {
              const stream = video.srcObject as MediaStream;
              stream.getTracks().forEach(track => track.stop());
            }
            setShowCamera(false);
          }
        }, 'image/jpeg', 0.95);
      }
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
    }
    setShowCamera(false);
    setCaptureMode('upload');
  };

  const removeFile = () => {
    setSelectedFile(null);
    setResult(null);
    setCaptureMode('upload');
    setShowCamera(false);
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center pt-16">
      <div className="container max-w-4xl mx-auto px-4">
        {!result ? (
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm mx-auto">
                ✨ AI-Powered Analysis
              </div>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl hero-title">
                <span className="bg-gradient-to-r from-violet-500 via-pink-500 to-yellow-500 bg-clip-text text-transparent">
                How Attractive Am I?
                </span>
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
                Get instant, AI-powered insights into your visual appeal and facial features
              </p>
            </div>

            <div className="text-center">
              <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
                Upload your photo to get a comprehensive &ldquo;How Attractive Am I&rdquo; analysis with overall score + 9 detailed factors
              </p>
            </div>

            <div className="space-y-6">
              {/* Image Selection Area */}
              <div className="space-y-4">
                {/* Mode Toggle */}
                <div className="flex gap-2 justify-center">
                  <Button
                    variant={captureMode === 'upload' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setCaptureMode('upload')}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Photo
                  </Button>
                  <Button
                    variant={captureMode === 'camera' ? 'default' : 'outline'}
                    size="sm"
                    onClick={startCamera}
                  >
                    <Camera className="mr-2 h-4 w-4" />
                    Snap Photo
                  </Button>
                </div>

                {/* Upload/Camera Area */}
                <div className="relative">
                  {showCamera && captureMode === 'camera' ? (
                    <div className="border-2 border-dashed border-violet-400 rounded-xl p-4 bg-violet-50">
                      <div className="text-center space-y-4">
                        <video
                          ref={videoRef}
                          autoPlay
                          playsInline
                          className="w-full h-64 object-cover rounded-lg bg-gray-200"
                        />
                        <canvas ref={canvasRef} className="hidden" />
                        <div className="flex gap-2 justify-center">
                          <Button onClick={capturePhoto}>
                            <Camera className="mr-2 h-4 w-4" />
                            Take Photo
                          </Button>
                          <Button variant="outline" onClick={stopCamera}>
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className={`border-2 border-dashed border-muted-foreground/25 rounded-xl p-8 transition-all duration-300 shadow-sm hover:shadow-md ${
                      dragActive ? 'border-violet-400 bg-violet-50 scale-105' : 'border-gray-300 hover:border-gray-400'
                    }`}
                         onDragEnter={handleDrag}
                         onDragLeave={handleDrag}
                         onDragOver={handleDrag}
                         onDrop={handleDrop}>
                      {!selectedFile ? (
                        <div className="text-center">
                          <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                          <p className="text-lg font-medium mb-2">
                            {captureMode === 'upload' ? 'Upload your photo' : 'Select a photo'}
                          </p>
                          <p className="text-muted-foreground mb-4">
                            Drag and drop or click to select an image
                          </p>
                          {captureMode === 'upload' && (
                            <>
                              <input
                                ref={fileInputRef}
                                type="file"
                                accept="image/*"
                                onChange={handleFileSelect}
                                className="hidden"
                              />
                              <Button
                                variant="outline"
                                className="mx-auto bg-yellow-400 hover:bg-yellow-500 text-black border-yellow-400 hover:border-yellow-500"
                                onClick={handleChooseFileClick}
                              >
                                <Upload className="mr-2 h-4 w-4" />
                                Choose File
                              </Button>
                            </>
                          )}
                          <p className="text-xs text-muted-foreground mt-2">
                            JPG, PNG up to 5MB • No face data is stored
                          </p>
                        </div>
                      ) : (
                        <div className="text-center">
                          <Image
                            src={URL.createObjectURL(selectedFile)}
                            alt="Selected"
                            width={200}
                            height={200}
                            className="mx-auto rounded-lg mb-4 object-cover"
                          />
                          <p className="font-medium mb-2">{selectedFile.name}</p>
                          <Button variant="outline" onClick={removeFile} size="sm">
                            Remove
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Button onClick={handleAnalysis} disabled={!selectedFile || isAnalyzing} size="lg" className="w-full max-w-xs">
              {isAnalyzing ? "Analyzing..." : "Analyze My Face"}
            </Button>
          </div>
        ) : (
          <div>
            <MultiAnalysisResults
              data={result}
              uploadedImage={selectedFile ? URL.createObjectURL(selectedFile) : undefined}
            />
            <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
              <Button
                onClick={() => {
                  setResult(null);
                  setSelectedFile(null);
                }}
                size="lg"
                className="px-12 py-4 text-xl font-bold bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
              >
                Analyze Another Photo
              </Button>

              <Button
                onClick={async () => {
                  try {
                    // Convert image to base64 for storage
                    const imageBase64 = selectedFile ? (await compressImageToBase64(selectedFile, 800, 800, 0.7)).base64 : undefined;
                    const saveResponse = await fetch('/api/results', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      body: JSON.stringify({
                        analysis_data: result,
                        image_base64: imageBase64,
                      }),
                    });

                    if (saveResponse.ok) {
                      const { id } = await saveResponse.json();
                      const shareUrl = `${window.location.origin}/results/${id}`;
                      await navigator.clipboard.writeText(shareUrl);
                      toast.success('Share link copied to clipboard!');
                    } else {
                      toast.error('Failed to create share link');
                    }
                  } catch (error) {
                    console.error('Error creating share link:', error);
                    toast.error('Failed to create share link');
                  }
                }}
                variant="outline"
                size="lg"
                className="px-12 py-4 text-xl font-semibold border-2 border-pink-500 text-pink-600 hover:bg-pink-50"
              >
                <Share2 className="mr-2 h-5 w-5" />
                Share Results
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
