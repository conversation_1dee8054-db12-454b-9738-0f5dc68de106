"use client";

import Image from "next/image";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Fitness Enthusiast",
      image: "/testimonials/user1.jpg",
      quote: "The facial symmetry and jawline analysis helped me understand my bone structure better. Now I know which angles work best for me and how to enhance my natural features.",
    },
    {
      name: "<PERSON>",
      role: "Content Creator",
      image: "/testimonials/user2.jpg",
      quote: "The detailed breakdown of my cheekbones and eye area was incredibly insightful. I've improved my grooming routine and photography based on the AI's recommendations.",
    },
    {
      name: "<PERSON>",
      role: "Personal Trainer",
      image: "/testimonials/user3.jpg",
      quote: "The masculinity and facial structure analysis gave me objective insights into my appearance. I've optimized my skincare routine and now understand my best features better.",
    },
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-purple-50 to-pink-100">
      <div className="container">
        <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl text-center mb-12">
          What Our Users Say
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="bg-background p-6 rounded-lg shadow-lg flex flex-col items-center text-center"
            >
              <div className="relative w-20 h-20 mb-4">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  fill
                  className="rounded-full object-cover"
                />
              </div>
              <blockquote className="mb-4 text-muted-foreground">
                &ldquo;{testimonial.quote}&rdquo;
              </blockquote>
              <cite className="not-italic">
                <div className="font-semibold">{testimonial.name}</div>
                <div className="text-sm text-muted-foreground">
                  {testimonial.role}
                </div>
              </cite>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
