import React from 'react';
import Navbar from '@/components/landing-page/navbar';
import Footer from '@/components/landing-page/footer';

export default function TermsOfService() {
  return (
    <>
      <Navbar />
      <div className="container mx-auto px-4 py-8 max-w-4xl pt-24">
        <h1 className="text-4xl font-bold mb-8">Terms of Service</h1>
        
        <div className="space-y-6">
          <section>
            <h2 className="text-2xl font-semibold mb-4">1. Acceptance of Terms</h2>
            <p className="text-muted-foreground">
              By accessing and using attractivenessscore.com services, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">2. Description of Service</h2>
            <p className="text-muted-foreground">
              attractivenessscore.com provides AI-powered analysis and generation services, including but not limited to attractiveness scoring and facial analysis.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">3. User Obligations</h2>
            <p className="text-muted-foreground">
              Users must:
            </p>
            <ul className="list-disc ml-6 mt-2 text-muted-foreground">
              <li>Be at least 18 years of age</li>
              <li>Provide accurate information when creating an account</li>
              <li>Maintain the security of their account credentials</li>
              <li>Use the service in compliance with these terms</li>
            </ul>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">4. Privacy and Data</h2>
            <p className="text-muted-foreground">
              We collect and process user data in accordance with our Privacy Policy. Users must consent to our data collection practices to use the service.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">5. Liability</h2>
            <p className="text-muted-foreground">
              We are not liable for any direct, indirect, incidental, consequential, or punitive damages resulting from your use of or inability to use the service.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">6. Changes to Terms</h2>
            <p className="text-muted-foreground">
              We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through the service.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">7. Termination</h2>
            <p className="text-muted-foreground">
              We reserve the right to terminate or suspend access to our service immediately, without prior notice, for any violation of these Terms of Service.
            </p>
          </section>

          <section>
            <h2 className="text-2xl font-semibold mb-4">8. Contact Information</h2>
            <p className="text-muted-foreground">
              For any questions about these Terms of Service, please contact <NAME_EMAIL>.
            </p>
          </section>
        </div>
      </div>
      <Footer />
    </>
  );
}
