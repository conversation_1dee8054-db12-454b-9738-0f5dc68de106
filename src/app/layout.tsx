import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "../components/theme/theme-provider";
import { ClerkProvider } from "@clerk/nextjs";
import MediavineAdScript from "../components/MediavineAdScript";
import GrowMeScript from "../components/GrowMeScript";
import UmamiAnalytics from "../components/UmamiAnalytics";
import StructuredData from "../components/seo/structured-data";

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "How Attractive Am I? - AI-Powered Facial Analysis | AttractivenessScore",
    template: "%s | AttractivenessScore"
  },
  description: "Discover your attractiveness score with our advanced AI-powered facial analysis. Get detailed insights on facial proportions, skin quality, and personalized beauty recommendations. Free online tool with instant results.",
  keywords: [
    "attractiveness score",
    "facial analysis",
    "AI beauty analysis",
    "face rating",
    "beauty score",
    "facial proportions",
    "face shape detector",
    "golden ratio face",
    "beauty assessment",
    "facial features analysis"
  ],
  authors: [{ name: "AttractivenessScore Team" }],
  creator: "AttractivenessScore",
  publisher: "AttractivenessScore",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://attractive-score.vercel.app'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    title: 'How Attractive Am I? - AI-Powered Facial Analysis',
    description: 'Discover your attractiveness score with our advanced AI-powered facial analysis. Get detailed insights and personalized beauty recommendations.',
    siteName: 'AttractivenessScore',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AttractivenessScore - AI-Powered Facial Analysis',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'How Attractive Am I? - AI-Powered Facial Analysis',
    description: 'Discover your attractiveness score with our advanced AI-powered facial analysis. Get detailed insights and personalized beauty recommendations.',
    images: ['/og-image.jpg'],
    creator: '@attractivescore',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head />
      <body suppressHydrationWarning className={poppins.className}>
        <StructuredData type="webapp" />
        <ClerkProvider publishableKey={process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY as string}>
          <ThemeProvider>
            {children}
            <MediavineAdScript />
            <GrowMeScript />
            <UmamiAnalytics />
          </ThemeProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
